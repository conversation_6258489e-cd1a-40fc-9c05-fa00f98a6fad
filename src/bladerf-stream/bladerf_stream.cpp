#include "bladerf_stream.h"
#include "logging/logging.h"
#include <algorithm>
#include <cmath>

BladeRFIQStream::BladeRFIQStream(const Config& cfg)
    : cfg_(cfg) {}

BladeRFIQStream::~BladeRFIQStream() {
    close();
}

bool BladeRFIQStream::open() {
    if (isOpen_) {
        close();
    }

    // Keep SDK logs reasonable
    bladerf_log_set_verbosity(BLADERF_LOG_LEVEL_WARNING);

    // Open first available device
    int st = bladerf_open(&dev_, nullptr);
    if (st != 0) {
        return setError(std::string("Failed to open bladeRF: ") + bladerf_strerror(st));
    }

    if (!configureDevice()) {
        bladerf_close(dev_);
        dev_ = nullptr;
        return false;
    }

    if (!configureSyncRx()) {
        bladerf_close(dev_);
        dev_ = nullptr;
        return false;
    }

    // Enable RX module
    st = bladerf_enable_module(dev_, BLADERF_CHANNEL_RX(cfg_.channel), true);
    if (st != 0) {
        setError(std::string("Failed to enable RX: ") + bladerf_strerror(st));
        bladerf_close(dev_);
        dev_ = nullptr;
        return false;
    }

    isOpen_ = true;
    active_ = true;

    // Log successful opening (simplified to avoid stream operator issues)
    std::string gainModeStr = (info_.gainMode == GainMode::MANUAL ? "MANUAL" :
                               info_.gainMode == GainMode::FAST   ? "FAST"   :
                               info_.gainMode == GainMode::SLOW   ? "SLOW"   : "DEFAULT");
    std::string logMsg = "bladeRF stream opened. freq=" + std::to_string(info_.frequencyHz) +
                        "Hz, rate=" + std::to_string(info_.sampleRateHz) +
                        "Hz, bw=" + std::to_string(info_.bandwidthHz) +
                        "Hz, ch=" + std::to_string(info_.channel) +
                        ", gainMode=" + gainModeStr;
    if (info_.gainMode == GainMode::MANUAL) {
        logMsg += ", manualGainDb=" + std::to_string(info_.manualGainDb);
    }
    // For now, just use a simple log without the macro
    // LOG_INFO(BLADERF_STREAM, logMsg);

    return true;
}

bool BladeRFIQStream::configureDevice() {
    if (!dev_) return setError("Device not open");

    // Frequency
    {
        int st = bladerf_set_frequency(dev_, BLADERF_CHANNEL_RX(cfg_.channel), static_cast<uint64_t>(cfg_.frequencyHz));
        if (st != 0) return setError(std::string("Set frequency failed: ") + bladerf_strerror(st));
        uint64_t actual = 0;
        st = bladerf_get_frequency(dev_, BLADERF_CHANNEL_RX(cfg_.channel), &actual);
        if (st == 0) info_.frequencyHz = static_cast<double>(actual);
        else return setError(std::string("Get frequency failed: ") + bladerf_strerror(st));
    }

    // Sample rate
    {
        uint32_t actual = 0;
        int st = bladerf_set_sample_rate(dev_, BLADERF_CHANNEL_RX(cfg_.channel),
                                         static_cast<uint32_t>(cfg_.sampleRateHz), &actual);
        if (st != 0) return setError(std::string("Set sample rate failed: ") + bladerf_strerror(st));
        info_.sampleRateHz = static_cast<double>(actual);
    }

    // Bandwidth
    {
        uint32_t actual = 0;
        int st = bladerf_set_bandwidth(dev_, BLADERF_CHANNEL_RX(cfg_.channel),
                                       static_cast<uint32_t>(cfg_.bandwidthHz), &actual);
        if (st != 0) return setError(std::string("Set bandwidth failed: ") + bladerf_strerror(st));
        info_.bandwidthHz = static_cast<double>(actual);
    }

    // Gain mode / Manual gain
    {
        bladerf_gain_mode mode = mapGainMode(cfg_.gainMode);
        int st = bladerf_set_gain_mode(dev_, BLADERF_CHANNEL_RX(cfg_.channel), mode);
        if (st != 0) return setError(std::string("Set gain mode failed: ") + bladerf_strerror(st));

        // Read back actual mode (defensive)
        bladerf_gain_mode actualMode = BLADERF_GAIN_DEFAULT;
        st = bladerf_get_gain_mode(dev_, BLADERF_CHANNEL_RX(cfg_.channel), &actualMode);
        if (st == 0) {
            // Map back
            switch (actualMode) {
                case BLADERF_GAIN_MANUAL:  info_.gainMode = GainMode::MANUAL;  break;
                case BLADERF_GAIN_FASTATTACK_AGC: info_.gainMode = GainMode::FAST; break;
                case BLADERF_GAIN_SLOWATTACK_AGC: info_.gainMode = GainMode::SLOW; break;
                default: info_.gainMode = GainMode::DEFAULT_; break;
            }
        } else {
            // If unable to read, assume configured
            info_.gainMode = cfg_.gainMode;
        }

        if (info_.gainMode == GainMode::MANUAL) {
            st = bladerf_set_gain(dev_, BLADERF_CHANNEL_RX(cfg_.channel), cfg_.manualGainDb);
            if (st != 0) return setError(std::string("Set manual gain failed: ") + bladerf_strerror(st));
            int actualGain = 0;
            st = bladerf_get_gain(dev_, BLADERF_CHANNEL_RX(cfg_.channel), &actualGain);
            if (st == 0) info_.manualGainDb = actualGain;
            else info_.manualGainDb = cfg_.manualGainDb;
        } else {
            info_.manualGainDb = 0;
        }
    }

    info_.channel = cfg_.channel;

    return true;
}

bool BladeRFIQStream::configureSyncRx() {
    if (!dev_) return setError("Device not open");

    int st = bladerf_sync_config(
        dev_,
        BLADERF_RX_X1,
        BLADERF_FORMAT_SC16_Q11,
        kNumBuffers,
        kSyncBufferSamples,
        kNumTransfers,
        /*stream_timeout_ms*/ 0  // per-call timeout is provided in readSamples()
    );
    if (st != 0) {
        return setError(std::string("Sync config failed: ") + bladerf_strerror(st));
    }
    return true;
}

bool BladeRFIQStream::readSamples(SampleType* dst, size_t sampleCount) {
    if (!isOpen_ || !active_) return false;
    if (!dst) return setError("readSamples: dst is null");
    if (sampleCount == 0) return true;

    size_t remaining = sampleCount;
    size_t written = 0;

    // Use a fixed-size buffer on the stack for small reads, or allocate once for large reads
    constexpr size_t kStackBufferSamples = 1024; // 4KB stack buffer for I/Q pairs
    int16_t stackBuffer[2 * kStackBufferSamples]; // I & Q
    std::vector<int16_t> heapBuffer;

    // Only allocate heap buffer if we need more than stack buffer can handle
    if (sampleCount > kStackBufferSamples) {
        heapBuffer.resize(2 * kSyncBufferSamples); // I & Q
    }

    while (remaining > 0 && active_) {
        const size_t toRead = std::min(remaining, static_cast<size_t>(kSyncBufferSamples));

        // Choose buffer based on read size
        int16_t* iqBuf = (toRead <= kStackBufferSamples) ? stackBuffer : heapBuffer.data();

        // Compute dynamic timeout: 2x expected time to read `toRead` samples
        const double expectedMs = (info_.sampleRateHz > 0.0)
            ? (1000.0 * static_cast<double>(toRead) / info_.sampleRateHz)
            : 1000.0; // fallback
        const unsigned int timeoutMs = static_cast<unsigned int>(
            std::clamp(expectedMs * 2.0, static_cast<double>(kMinTimeoutMs), static_cast<double>(kMaxTimeoutMs))
        );

        int timeouts = 0;
        while (true) {
            // Blocking read directly into chosen buffer
            int st = bladerf_sync_rx(dev_, iqBuf, static_cast<unsigned int>(toRead), nullptr, timeoutMs);
            if (st == 0) {
                // Pack samples directly from buffer to destination
                for (size_t i = 0; i < toRead; ++i) {
                    const int16_t I = iqBuf[2 * i];
                    const int16_t Q = iqBuf[2 * i + 1];
                    dst[written + i] = packSample(I, Q);
                }
                written += toRead;
                remaining -= toRead;
                break; // read next chunk
            } else if (st == BLADERF_ERR_TIMEOUT) {
                if (++timeouts > kMaxConsecutiveTimeouts) {
                    // LOG_ERROR(BLADERF_STREAM, "RX timeout after retries");
                    active_ = false;
                    return setError("RX timeout");
                }
                // retry
                continue;
            } else {
                active_ = false;
                return setError(std::string("RX failed: ") + bladerf_strerror(st));
            }
        }
    }

    return written == sampleCount;
}

void BladeRFIQStream::close() noexcept {
    active_ = false;

    if (dev_) {
        // Best-effort disable RX; if it fails, continue
        int st = bladerf_enable_module(dev_, BLADERF_CHANNEL_RX(cfg_.channel), false);
        if (st != 0) {
            // LOG_ERROR(BLADERF_STREAM, "Disable RX failed during close");
        }
        bladerf_close(dev_);
        dev_ = nullptr;
    }

    isOpen_ = false;
}

SampleRateType BladeRFIQStream::sampleRate() const noexcept {
    // IIQStream requires uint32_t; use actual
    const double sr = info_.sampleRateHz > 0.0 ? info_.sampleRateHz : cfg_.sampleRateHz;
    const uint64_t clamped = std::min<uint64_t>(static_cast<uint64_t>(std::llround(sr)), 0xFFFFFFFFull);
    return static_cast<SampleRateType>(clamped);
}

const std::string& BladeRFIQStream::sourceName() const noexcept {
    return sourceName_;
}

bool BladeRFIQStream::isActive() const noexcept {
    return isOpen_ && active_;
}

const std::string& BladeRFIQStream::lastError() const noexcept {
    return lastError_;
}

BladeRFIQStream::ResolvedInfo BladeRFIQStream::getResolvedInfo() const noexcept {
    return info_;
}

bool BladeRFIQStream::setError(const std::string& err) {
    lastError_ = err;
    return false;
}

bladerf_gain_mode BladeRFIQStream::mapGainMode(GainMode m) {
    switch (m) {
        case GainMode::MANUAL:   return BLADERF_GAIN_MANUAL;
        case GainMode::FAST:     return BLADERF_GAIN_FASTATTACK_AGC;
        case GainMode::SLOW:     return BLADERF_GAIN_SLOWATTACK_AGC;
        case GainMode::DEFAULT_: return BLADERF_GAIN_DEFAULT;
    }
    return BLADERF_GAIN_DEFAULT;
}