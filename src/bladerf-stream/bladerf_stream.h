#ifndef BLADERF_IQ_STREAM_H
#define BLADERF_IQ_STREAM_H

#include "../iiq-stream/iiq_stream.h"
#include <libbladeRF.h>
#include <string>
#include <atomic>
#include <vector>
#include <mutex>

// Logging token expected: BLADERF_STREAM
#include "logging/logging.h"

/**
 * BladeRFIQStream (synchronous pull)
 *
 * - No internal threads. The consumer calls readSamples(), which blocks until
 *   exactly `sampleCount` samples are read, or times out/fails.
 * - Uses libbladeRF sync interface (BLADERF_RX_X1, SC16_Q11).
 * - Applies configuration (frequency, sample rate, bandwidth, gain mode/manual gain).
 * - Stores "actual" values returned by the device and exposes them via getResolvedInfo().
 * - Computes a per-call timeout as 2x expected time to read requested samples,
 *   clamped to [kMinTimeoutMs, kMaxTimeoutMs]. Retries a couple times on timeout.
 */
class BladeRFIQStream : public IIQStream {
public:
    enum class GainMode {
        MANUAL,
        FAST,
        SLOW,
        DEFAULT_
    };

    struct Config {
        double frequencyHz = 915e6;
        double sampleRateHz = 1.0e6;
        double bandwidthHz = 1.5e6;
        int channel = 0;            // RX(0) or RX(1) if available
        GainMode gainMode = GainMode::MANUAL;
        int manualGainDb = 30;      // Used only when gainMode == MANUAL
    };

    struct ResolvedInfo {
        std::string sourceName = "bladeRF";
        double frequencyHz = 0.0;
        double sampleRateHz = 0.0;
        double bandwidthHz = 0.0;
        int channel = 0;
        GainMode gainMode = GainMode::MANUAL;
        int manualGainDb = 0;       // Valid when mode is MANUAL
    };

    explicit BladeRFIQStream(const Config& cfg);
    ~BladeRFIQStream() override;

    // Lifecycle
    bool open();
    void close() noexcept override;

    // IIQStream
    bool readSamples(SampleType* dst, size_t sampleCount) override;
    SampleRateType sampleRate() const noexcept override;
    const std::string& sourceName() const noexcept override;
    bool isActive() const noexcept override;
    const std::string& lastError() const noexcept override;

    // Access resolved (actual) parameters after open()
    ResolvedInfo getResolvedInfo() const noexcept;

private:
    // Internal helpers
    bool setError(const std::string& err);
    bool configureDevice();
    bool configureSyncRx();

    // Conversions / mappings
    inline SampleType packSample(int16_t i, int16_t q) const noexcept {
        return static_cast<SampleType>(static_cast<uint16_t>(i)) |
               (static_cast<SampleType>(static_cast<uint16_t>(q)) << 16);
    }
    static bladerf_gain_mode mapGainMode(GainMode m);

private:
    // Device
    struct bladerf* dev_ = nullptr;
    std::string lastError_;
    std::string sourceName_ = "bladeRF";

    // State
    std::atomic<bool> isOpen_{false};
    std::atomic<bool> active_{false};

    // Config and resolved info
    Config cfg_;
    ResolvedInfo info_;

    // Sync config (internal; not exposed to outer config)
    static constexpr unsigned int kNumBuffers = 32;
    static constexpr unsigned int kNumTransfers = 8;
    static constexpr unsigned int kSyncBufferSamples = 4096; // multiple of 1024

    // Per-call timeout policy
    static constexpr unsigned int kMinTimeoutMs = 20;
    static constexpr unsigned int kMaxTimeoutMs = 5000;
    static constexpr int kMaxConsecutiveTimeouts = 2;
};

#endif // BLADERF_IQ_STREAM_H