#pragma once
#include <node_object_wrap.h>
#include <v8.h>
#include <uv.h>
#include <memory>
#include <atomic>
#include <string>
#include "video-processor/video_processor.h"
#include "./node/thread_runner.h"
#include "codes.h"

namespace VideoDecodingAddon {

/**
 * VideoProcessorWrapper - Node.js object wrapper for IQVideoProcessor::VideoProcessor
 * One-shot lifecycle: start() -> [frames/errors] -> stop() -> onStop -> disposed
 */
class VideoProcessorWrapper final : public node::ObjectWrap {
public:
  // Node-specific methods / exports / prototypes
  static void Node_Init(v8::Local<v8::Object> exports);
  static void Node_New(const v8::FunctionCallbackInfo<v8::Value>& args);
  static void Stop_Prototype(const v8::FunctionCallbackInfo<v8::Value>& args);
  static v8::Persistent<v8::Function> constructor;

  VideoProcessorWrapper() = default;
  ~VideoProcessorWrapper() override;

  [[nodiscard]] bool start(v8::Local<v8::Object> configObject);
  void stop();

private:
  bool initializeNodeCallbacks(const v8::Local<v8::Object>& configObject);
  [[nodiscard]] std::unique_ptr<IIQStream> createIQStream(const v8::Local<v8::Object>& configObject) const;

  // Finalize stop on the Node thread after VideoProcessor has fully torn down.
  void finalizeStopOnNodeThread(int code);

  // JS emission helpers (must run on Node thread)
  void emitFramesToJS() const;
  void emitStopToJS(int code) const;
  void emitErrorToJS(ErrorCode code, const std::string& message) const;

  // Instance members
  std::unique_ptr<IQVideoProcessor::VideoProcessor> videoProcessor_;
  std::unique_ptr<NodeHelpers::NodeThreadRunner> nodeRunner_;

  // Callback storage for JavaScript callbacks
  v8::Persistent<v8::Function> onStopCallback_;
  v8::Persistent<v8::Function> onErrorCallback_;
  v8::Persistent<v8::Function> onFrameCallback_;

  // Stop state
  std::atomic<bool> stopping_{false};
  std::atomic<bool> stopEmitted_{false};
  std::atomic<bool> errorEmitted_{false};
};

} // namespace VideoDecodingAddon
